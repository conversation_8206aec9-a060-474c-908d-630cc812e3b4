import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.impl.NotifyExecutor;
import com.yunqu.yc.api.log.TaskLogger;

public class Demo {

    public static void main(String[] args) {

        try {
            Thread  thread = new Thread(new EventDispatcher());
            thread.start();
        } catch (Exception ex) {
            TaskLogger.getLogger().error("GlobalContextListener.contextInitialized() error,cause:" + ex.getMessage(), ex);
        }

        for (int i = 0; i < 5; i++) {
            new Thread(() -> {
                for (int i1 = 0; i1 < 50000; i1++) {
                    EventDispatcher.addEvent(new NotifyExecutor("STAT-TASK-RESULT-", new JSONObject()));
                    try {
                        Thread.sleep(100L);
                    } catch (InterruptedException e) {

                    }
                }
            }).start();

        }
    }
}
