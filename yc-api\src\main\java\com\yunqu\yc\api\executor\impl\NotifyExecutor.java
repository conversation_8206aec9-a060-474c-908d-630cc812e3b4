package com.yunqu.yc.api.executor.impl;

import java.util.List;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.executor.AbstractExecutor;
import com.yunqu.yc.api.log.TaskLogger;

public   class NotifyExecutor extends AbstractExecutor {
	
	/**
	 * 事件对象
	 */
	private  JSONObject params;
	
	private  String servicePrefix;
	
	
	public NotifyExecutor(String servicePrefix,JSONObject params){
		this.params = params;
		this.servicePrefix = servicePrefix;
	}
	
	public JSONObject getMsg() {
		return params;
	}
	
	public String getSessionId(){
		return RandomKit.randomStr();
	}
	
	public void execute(){
		long timer = System.currentTimeMillis();
//		if(ServerContext.isDebug()) TaskLogger.getLogger().info("[NotifyExecutor]执行外部服务["+servicePrefix+"]通知,params->"+params);
		String serviceId = servicePrefix;
		try {
			serviceId = this.handleMessage();
		} catch (Exception ex) {
//			TaskLogger.getLogger().error("[NotifyExecutor]执行通知服务["+serviceId+"] 失败, 原因:"+ex.getMessage()+" << "+params,ex);
		}
		timer = System.currentTimeMillis() - timer;
		if(timer>1000){
//			TaskLogger.getLogger().warn("[NotifyExecutor]服务["+serviceId+"]消息处理过长:"+timer+"ms,params->"+params);
		}
	}
	
	public String toString(){
		return "EventExecutor["+this.getSessionId()+"]->"+params;
	}
	
	public String handleMessage() throws Exception {
		String _serviceId =  this.servicePrefix;
		try {
			List<String> serviceIds = ServiceContext.findByPrefix(servicePrefix);
			if(serviceIds!=null && serviceIds.size()>0){
				for(String serviceId:serviceIds){
					_serviceId = serviceId;
					try {
						IService service = ServiceContext.getService(serviceId);
						if(service!=null){
//							TaskLogger.getLogger().info("["+serviceId+"]["+Thread.currentThread().getId()+"] >> "+params);
							long timer = System.currentTimeMillis();
							JSONObject result = service.invoke(params);
							timer = System.currentTimeMillis()-timer;
//							TaskLogger.getLogger().info("["+serviceId+"]["+Thread.currentThread().getId()+"][time:"+timer+"ms] << "+result);
						}
					} catch (Exception ex) {
//						TaskLogger.getLogger().error("[NotifyExecutor]通知服务失败["+serviceId+"]，原因："+ex.getMessage(),ex);
					}
				}
			}
		} catch (Exception ex) {
//			TaskLogger.getLogger().error("[NotifyExecutor]调用通知服务失败["+_serviceId+"]失败，原因："+ex.getMessage(),ex);
		}
		return _serviceId;
	}
	
}


