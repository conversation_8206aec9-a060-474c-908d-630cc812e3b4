package com.yunqu.yc.api.executor;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.TaskLogger;


public class EventHandlerThread implements Runnable{
	
	private   String sessionId = "";
	private   BlockingQueue<AbstractExecutor> eventQueue = new LinkedBlockingQueue<AbstractExecutor>();
	private   long threadId = 0 ;	
	private   boolean handleState = false;
	
	public EventHandlerThread(long threadId){
		this.threadId = threadId;
	}
	
	
	
	public String toString(){
		 return "HandlerThread->threadId:"+this.threadId+",eventQueue.size():"+eventQueue.size()+",sessionId:"+this.sessionId;
	}
	
	
	@Override
	public void run() {
		while(GlobalContextListener.runState){
			try {
				AbstractExecutor executor = eventQueue.poll(100, TimeUnit.MILLISECONDS);
//				System.out.println(Thread.currentThread().getName()+"-> eventQueue.poll "+"["+sessionId+"]");
				if(executor != null) {
//					System.out.println(Thread.currentThread().getName()+"-> executor");
//					TaskLogger.getLogger().info("EventHandlerThread["+this.threadId+"] EventExecutor.execute() -> "+executor);
					try {
						executor.execute();
					} catch (Exception ex) {
						ex.printStackTrace();
//						TaskLogger.getLogger().error("[ERROR]EventHandlerThread.execute() error , cause:"+ex.getMessage(),ex);
					}
					continue;
				}
			} catch (Exception ex) {
//				TaskLogger.getLogger().error("[ERROR]EventHandlerThread.run() error , cause:"+ex.getMessage(),ex);
			}

//			System.out.println(Thread.currentThread().getName()+"-> handleState -> "+handleState);
			if(this.handleState){
				EventDispatcher.removeSession(this.sessionId);
				EventDispatcher.addHandler(this,System.currentTimeMillis());
				this.handleState = false;
			}
			
		}
	}

	/**
	 * 把事件处理放进线程中进行处理。
	 * @param eventObject
	 */
	public void addEvent(AbstractExecutor executor){
		try {
			this.sessionId = executor.getSessionId();
			eventQueue.add(executor);
		} catch (Exception ex) {
//			TaskLogger.getLogger().error("[ERROR]EventHandlerThread.addEvent() error,cause:"+ex.getMessage(),ex);
		}
		handleState = true;
	}
	
	//append到当前处理的队列中
	public void appendEvent(AbstractExecutor executor) {
		if(eventQueue.size()>2000){
//			TaskLogger.getLogger().warn("[WARN]EventHandlerThread.addEvent() 待处理的执行器超过2000，丢弃当前的执行器,DBExecutor->:"+executor);
			return;
		}
		try {
			eventQueue.add(executor);
		} catch (Exception ex) {
//			TaskLogger.getLogger().error("[ERROR]EventHandlerThread.appendEvent() error,cause:"+ex.getMessage(),ex);
		}
	}

}
