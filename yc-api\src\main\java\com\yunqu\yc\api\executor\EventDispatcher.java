package com.yunqu.yc.api.executor;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.TaskLogger;


public class EventDispatcher implements Runnable{
	
	//保持目前mediagw所有的消息处理，消息进入该队列后统一处理。
	private  static BlockingQueue<AbstractExecutor> messageQueue = new LinkedBlockingQueue<AbstractExecutor>();
	/**
	 * 保存当前线程池的信息，如果线程空闲，则重新进入队列重新排队。
	 */
	private  static BlockingQueue<EventHandlerThread> threadQueue = new LinkedBlockingQueue<EventHandlerThread>();
	/**
	 * 保存当前session和线程的处理关系。
	 */
	private  static Map<String,EventHandlerThread>  sessionHandlers = new  ConcurrentHashMap <String,EventHandlerThread>();
	
	private static ThreadGroup  groups = new ThreadGroup("EventThreadGroup");
	
	private  long    threadCount = 0 ;
	
	private  long    messageCount = 0 ;
	
	//最大的处理线程数
	private  static int MAX_THREAD_COUNT = 50;
	
	public EventDispatcher(){
	}
	
	private  EventHandlerThread newHandlerThread(){
		if(threadCount > MAX_THREAD_COUNT) return null;
		threadCount++;
		EventHandlerThread handlerThread =  new EventHandlerThread(threadCount);
		Thread thread = new Thread(groups,handlerThread);
		thread.start();
		return handlerThread;
	}

	
	/**
	 * 把事件处理到处理队列中
	 * @param msgObj
	 */
	public static void addEvent(AbstractExecutor executor){
//		TaskLogger.getLogger().info("EventDispatcher[queueSize:"+messageQueue.size()+"] << "+executor);
		System.out.println("EventDispatcher[queueSize:"+messageQueue.size()+"] << "+executor);
		if(messageQueue.size()>50000){
//			TaskLogger.getLogger().warn("EventDispatcher messageQueue.size() over "+messageQueue.size()+",不处理Executor。");
			return;
		}
		//System.out.println("EventHandler.addEvent(),queueSize:"+messageQueue.size()+",sessionId->"+eventObject.getSessionId());
		messageQueue.add(executor);
	}
	
	/**
	 * 把线程返回到消息处理队列
	 * @param handler
	 */
	public static  boolean  addHandler(EventHandlerThread handler,long timer){
		
		while(GlobalContextListener.runState){
			try {
				threadQueue.add(handler);
				return true;
			} catch (Exception ex) {
//				TaskLogger.getLogger().error("[ERROR]EventDispather  threadQueue.add(handler) error,cause:"+ex.getMessage(),ex);
				try {
					Thread.sleep(500);
				} catch (Exception ex1) {
//					TaskLogger.getLogger().error(ex1,ex1);
				}
			}
		}
		return true;
	}
	
	/**
	 * 移除当前处理正在处理的sessionId
	 * @param sessionId
	 */
	public static void   removeSession(String sessionId){
		try {
			sessionHandlers.remove(sessionId);
			//增加保护为了防止内存泄漏
			if(sessionHandlers.size()>10000) sessionHandlers.clear();
		} catch (Exception ex) {
//			TaskLogger.getLogger().error("[ERROR]EventDispather  sessionHandlers.remove("+sessionId+") error,cause:"+ex.getMessage(),ex);
		}
	}

	@Override
	public void run() {
		while(GlobalContextListener.runState){
			try {
				AbstractExecutor executor = messageQueue.poll(10, TimeUnit.MILLISECONDS);
				if(executor == null) continue;
				//System.out.println("Take event by messageQueue,messageQueueSize:"+messageQueue.size()+",handleThreadSize:"+threadQueue.size()+",sessionId->"+eventObject.getSessionId());
				//这里先判断一下，session在那个handler里面，如果在则用回原来的正在处理的线程。
				EventHandlerThread handler = sessionHandlers.get(executor.getSessionId());
				
				messageCount++;
				if(messageCount > Integer.MAX_VALUE) messageCount = 1;
				if(messageCount%200==0) {
//					TaskLogger.getLogger().info("[DEBUG] EventDispatcher 当前处理信息->已处理消息量："+messageCount+",已创建线程数："+threadCount+",线程组活动线程数："+groups.activeCount()+",队列线程数："+threadQueue.size()+",待处理消息数:"+messageQueue.size());
				}
				if(handler!=null) {
					//System.out.println("当前session已经在线程中处理，当前线程为：->"+handler);
					//把session和处理线程的关系进行绑定
					handler.appendEvent(executor);
					continue;
				}
				//从空闲的线程中找到一个线程进行处理。
				EventHandlerThread idleThread = this.getIdleHandler();
				if(idleThread!=null){
					sessionHandlers.put(executor.getSessionId(), idleThread);
					idleThread.addEvent(executor);
				}
			} catch (Exception ex) {
//				TaskLogger.getLogger().error(ex,ex);
				try {
					Thread.sleep(200);
				} catch (Exception ex1) {
				}
			}
		}
	}
	
	private EventHandlerThread getIdleHandler(){
		while(GlobalContextListener.runState){
			try {
				EventHandlerThread eventThread  =  threadQueue.poll(5, TimeUnit.MILLISECONDS);
				//如果没有可用的线程，则不做处理
				if(eventThread == null) {
					eventThread = this.newHandlerThread();
				}
				if(eventThread == null) continue;
				//如果线程处于激活状态，则不采用当前线程进行处理。
				return eventThread;
			} catch (Exception ex) {
//				TaskLogger.getLogger().error("[ERROR]EventDispatcher HandlerThread.getIdleHandler() error,cause:"+ex.getMessage(),ex);
				try {
					Thread.sleep(200);
				} catch (Exception ex1) {
//					TaskLogger.getLogger().error(ex1,ex1);
				}
			}
		}
		return null;
	} 
}
