"RMI TCP Accept-0" #36 daemon prio=5 os_prio=0 tid=0x000000002d9e0800 nid=0x6128 runnable [0x000000002efbe000]
   java.lang.Thread.State: RUNNABLE
	at java.net.DualStackPlainSocketImpl.accept0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketAccept(DualStackPlainSocketImpl.java:131)
	at java.net.AbstractPlainSocketImpl.accept(AbstractPlainSocketImpl.java:409)
	at java.net.PlainSocketImpl.accept(PlainSocketImpl.java:199)
	- locked <0x000000071eb6d470> (a java.net.SocksSocketImpl)
	at java.net.ServerSocket.implAccept(ServerSocket.java:545)
	at java.net.ServerSocket.accept(ServerSocket.java:513)
	at sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:52)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:405)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.lang.Thread.run(Thread.java:748)


"Thread-0" #20 prio=5 os_prio=0 tid=0x000000002bf58000 nid=0x566c waiting on condition [0x000000002d1af000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071eb38218> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventDispatcher.getIdleHandler(EventDispatcher.java:138)
	at com.yunqu.yc.api.executor.EventDispatcher.run(EventDispatcher.java:120)
	at java.lang.Thread.run(Thread.java:748)


"Thread-58" #82 prio=5 os_prio=0 tid=0x000000002d499800 nid=0x11c0 waiting on condition [0x000000003299e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e73bc0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-57" #81 prio=5 os_prio=0 tid=0x000000002d495800 nid=0x7160 waiting on condition [0x000000003289f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e728b8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-56" #80 prio=5 os_prio=0 tid=0x000000002d498800 nid=0x4f80 waiting on condition [0x000000003279f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e71550> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-55" #79 prio=5 os_prio=0 tid=0x000000002d495000 nid=0x7874 waiting on condition [0x000000003269e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e701e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-54" #78 prio=5 os_prio=0 tid=0x000000002d497000 nid=0x8654 waiting on condition [0x000000003259f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e6eea0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-53" #77 prio=5 os_prio=0 tid=0x000000002d496800 nid=0x6600 waiting on condition [0x000000003249e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e34738> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-52" #76 prio=5 os_prio=0 tid=0x000000002d498000 nid=0x3f14 waiting on condition [0x000000003239f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e33748> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-51" #75 prio=5 os_prio=0 tid=0x000000002daca800 nid=0x60dc waiting on condition [0x000000003229f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e32e08> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-50" #74 prio=5 os_prio=0 tid=0x000000002dac9800 nid=0x6cd0 waiting on condition [0x000000003219f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e32110> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-49" #73 prio=5 os_prio=0 tid=0x000000002dac7800 nid=0x5e10 waiting on condition [0x000000003209f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e31aa8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-48" #72 prio=5 os_prio=0 tid=0x000000002dac6000 nid=0x8808 waiting on condition [0x0000000031f9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e31440> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-47" #71 prio=5 os_prio=0 tid=0x000000002dac9000 nid=0x2304 waiting on condition [0x00000000016ae000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e20c88> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-46" #70 prio=5 os_prio=0 tid=0x000000002dac8000 nid=0x7384 waiting on condition [0x00000000014af000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e1f960> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-45" #69 prio=5 os_prio=0 tid=0x000000002dac4800 nid=0x5a8c waiting on condition [0x0000000031c9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e1e320> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-44" #68 prio=5 os_prio=0 tid=0x000000002dac3800 nid=0x51fc waiting on condition [0x0000000031b9e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e1d330> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-43" #67 prio=5 os_prio=0 tid=0x000000002dac6800 nid=0x773c waiting on condition [0x0000000031a9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e1ccc8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-42" #66 prio=5 os_prio=0 tid=0x000000002dac2000 nid=0x431c waiting on condition [0x000000003199e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e18db0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-41" #65 prio=5 os_prio=0 tid=0x000000002dac5000 nid=0x8178 waiting on condition [0x000000003189f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e17aa8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-40" #64 prio=5 os_prio=0 tid=0x000000002dac1800 nid=0x70dc waiting on condition [0x000000003179f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e13748> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-39" #63 prio=5 os_prio=0 tid=0x000000002dac3000 nid=0x814c waiting on condition [0x000000003169e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e12188> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-38" #62 prio=5 os_prio=0 tid=0x000000002dabd800 nid=0x6e1c waiting on condition [0x000000003159e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e10e80> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-37" #61 prio=5 os_prio=0 tid=0x000000002dac0800 nid=0x5730 waiting on condition [0x000000003149f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e0f8a0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-36" #60 prio=5 os_prio=0 tid=0x000000002dac0000 nid=0x7ce8 waiting on condition [0x000000003139f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e0e5b8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-35" #59 prio=5 os_prio=0 tid=0x000000002dabf000 nid=0x5c84 waiting on condition [0x000000003129f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e0d588> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-34" #58 prio=5 os_prio=0 tid=0x000000002dabb800 nid=0x7550 waiting on condition [0x000000003119f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e0c558> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-33" #57 prio=5 os_prio=0 tid=0x000000002dabd000 nid=0x3530 waiting on condition [0x000000003109f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x0000000716e0b250> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-32" #56 prio=5 os_prio=0 tid=0x000000002dabc000 nid=0x31e4 waiting on condition [0x0000000030f9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea81050> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-31" #55 prio=5 os_prio=0 tid=0x000000002dabe800 nid=0x5d68 waiting on condition [0x0000000030e9e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea881a8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-30" #54 prio=5 os_prio=0 tid=0x000000002d9ef000 nid=0x5594 waiting on condition [0x0000000030d9e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea883c8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-29" #53 prio=5 os_prio=0 tid=0x000000002d9ed800 nid=0x313c waiting on condition [0x0000000030c9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea885e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-28" #52 prio=5 os_prio=0 tid=0x000000002d9ef800 nid=0x57d8 waiting on condition [0x0000000030b9e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea88808> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-27" #51 prio=5 os_prio=0 tid=0x000000002d9ec800 nid=0x521c waiting on condition [0x0000000030a9f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea88a28> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-26" #50 prio=5 os_prio=0 tid=0x000000002d9ee000 nid=0x394c waiting on condition [0x000000003099f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea88d00> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-25" #49 prio=5 os_prio=0 tid=0x000000002d9e9000 nid=0x8b20 waiting on condition [0x000000003089f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea892a0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-24" #48 prio=5 os_prio=0 tid=0x000000002d9ec000 nid=0x4eb4 waiting on condition [0x000000003079f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea89ff8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-23" #47 prio=5 os_prio=0 tid=0x000000002d9e8000 nid=0x4864 waiting on condition [0x000000003069f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8a3a0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-22" #46 prio=5 os_prio=0 tid=0x000000002d9ea800 nid=0x506c waiting on condition [0x000000003059e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8a748> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-21" #45 prio=5 os_prio=0 tid=0x000000002d9eb000 nid=0x5600 waiting on condition [0x000000003049e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8aaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-20" #44 prio=5 os_prio=0 tid=0x000000002d9e7800 nid=0x8170 waiting on condition [0x000000003039f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8ae98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-19" #43 prio=5 os_prio=0 tid=0x000000002d9e9800 nid=0x778 waiting on condition [0x000000003029f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8b240> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-18" #42 prio=5 os_prio=0 tid=0x000000002d9e3800 nid=0x5f50 waiting on condition [0x000000003019f000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8b5e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-17" #41 prio=5 os_prio=0 tid=0x000000002d9e6800 nid=0x46e4 waiting on condition [0x000000003009e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8b990> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-15" #37 prio=5 os_prio=0 tid=0x000000002d9e1800 nid=0x1bc4 waiting on condition [0x000000002f2bf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8bd38> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-14" #35 prio=5 os_prio=0 tid=0x000000002d9e3000 nid=0x50c4 waiting on condition [0x000000002eebf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8c0e0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-13" #34 prio=5 os_prio=0 tid=0x000000002d9e2000 nid=0x702c waiting on condition [0x000000002edbf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8c488> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-12" #33 prio=5 os_prio=0 tid=0x000000002bccf000 nid=0x4dac waiting on condition [0x000000002ecbf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8c830> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-11" #32 prio=5 os_prio=0 tid=0x000000002bcce000 nid=0x7838 waiting on condition [0x000000002ebbf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8cbd8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-10" #31 prio=5 os_prio=0 tid=0x000000002bccd800 nid=0x6844 waiting on condition [0x000000002eabf000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8cf80> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-9" #30 prio=5 os_prio=0 tid=0x000000002dec2800 nid=0x2154 waiting on condition [0x000000002e89e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8d320> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-8" #29 prio=5 os_prio=0 tid=0x000000002d66f000 nid=0x4abc waiting on condition [0x000000002e9be000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8d6c0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"Thread-7" #28 prio=5 os_prio=0 tid=0x000000002d56c800 nid=0x8144 waiting on condition [0x000000002e79e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071ea8da60> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
	at com.yunqu.yc.api.executor.EventHandlerThread.run(EventHandlerThread.java:33)
	at java.lang.Thread.run(Thread.java:748)


"JMX server connection timeout 40" #40 daemon prio=5 os_prio=0 tid=0x000000002d9e6000 nid=0x5f90 in Object.wait() [0x000000002f5bf000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(Native Method)
	- waiting on <0x000000071ea8de98> (a [I)
	at com.sun.jmx.remote.internal.ServerCommunicatorAdmin$Timeout.run(ServerCommunicatorAdmin.java:168)
	- locked <0x000000071ea8de98> (a [I)
	at java.lang.Thread.run(Thread.java:748)


"RMI TCP Connection(1)-192.168.31.13" #38 daemon prio=5 os_prio=0 tid=0x000000002d9e4800 nid=0x59e8 runnable [0x000000002f3bd000]
   java.lang.Thread.State: RUNNABLE
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265)
	- locked <0x000000071eb6f780> (a java.io.BufferedInputStream)
	at java.io.FilterInputStream.read(FilterInputStream.java:83)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:555)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler$$Lambda$4/1303878883.run(Unknown Source)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)


"Monitor Ctrl-Break" #6 daemon prio=5 os_prio=0 tid=0x000000002bc18800 nid=0x3750 runnable [0x000000002c1ae000]
   java.lang.Thread.State: RUNNABLE
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	- locked <0x000000071ebbc408> (a java.io.InputStreamReader)
	at java.io.InputStreamReader.read(InputStreamReader.java:184)
	at java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.io.BufferedReader.readLine(BufferedReader.java:324)
	- locked <0x000000071ebbc408> (a java.io.InputStreamReader)
	at java.io.BufferedReader.readLine(BufferedReader.java:389)
	at com.intellij.rt.execution.application.AppMainV2$1.run(AppMainV2.java:31)


"RMI Scheduler(0)" #39 daemon prio=5 os_prio=0 tid=0x000000002d9e5000 nid=0x2140 waiting on condition [0x000000002f4be000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at sun.misc.Unsafe.park(Native Method)
	- parking to wait for  <0x000000071eae8188> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
	at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)


"Thread-5" #25 prio=5 os_prio=0 tid=0x000000002d254000 nid=0x429c waiting on condition [0x000000002e67f000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(Native Method)
	at Demo.lambda$main$0(Demo.java:22)
	at Demo$$Lambda$1/1963387170.run(Unknown Source)
	at java.lang.Thread.run(Thread.java:748)


"Thread-4" #24 prio=5 os_prio=0 tid=0x000000002d253800 nid=0x89d0 waiting on condition [0x000000002e57f000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(Native Method)
	at Demo.lambda$main$0(Demo.java:22)
	at Demo$$Lambda$1/1963387170.run(Unknown Source)
	at java.lang.Thread.run(Thread.java:748)


"Thread-3" #23 prio=5 os_prio=0 tid=0x000000002d252800 nid=0x7e74 waiting on condition [0x000000002e47f000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(Native Method)
	at Demo.lambda$main$0(Demo.java:22)
	at Demo$$Lambda$1/1963387170.run(Unknown Source)
	at java.lang.Thread.run(Thread.java:748)


"Thread-2" #22 prio=5 os_prio=0 tid=0x000000002d252000 nid=0x6be4 waiting on condition [0x000000002e37f000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(Native Method)
	at Demo.lambda$main$0(Demo.java:22)
	at Demo$$Lambda$1/1963387170.run(Unknown Source)
	at java.lang.Thread.run(Thread.java:748)


"Thread-1" #21 prio=5 os_prio=0 tid=0x000000002d24f000 nid=0xbf4 waiting on condition [0x000000002e27e000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(Native Method)
	at Demo.lambda$main$0(Demo.java:22)
	at Demo$$Lambda$1/1963387170.run(Unknown Source)
	at java.lang.Thread.run(Thread.java:748)


"Finalizer" #3 daemon prio=8 os_prio=1 tid=0x000000002925b000 nid=0x89fc in Object.wait() [0x000000002b6ae000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(Native Method)
	- waiting on <0x000000071ebbe490> (a java.lang.ref.ReferenceQueue$Lock)
	at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
	- locked <0x000000071ebbe490> (a java.lang.ref.ReferenceQueue$Lock)
	at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:165)
	at java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:216)


"Reference Handler" #2 daemon prio=10 os_prio=2 tid=0x00000000272a9800 nid=0x50b8 in Object.wait() [0x000000002b5af000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(Native Method)
	- waiting on <0x000000071ebbe648> (a java.lang.ref.Reference$Lock)
	at java.lang.Object.wait(Object.java:502)
	at java.lang.ref.Reference.tryHandlePending(Reference.java:191)
	- locked <0x000000071ebbe648> (a java.lang.ref.Reference$Lock)
	at java.lang.ref.Reference$ReferenceHandler.run(Reference.java:153)


"DestroyJavaVM" #26 prio=5 os_prio=0 tid=0x00000000039b7000 nid=0x562c waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"Service Thread" #19 daemon prio=9 os_prio=0 tid=0x000000002bde0800 nid=0x468c runnable [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C1 CompilerThread11" #18 daemon prio=9 os_prio=2 tid=0x000000002bcc9800 nid=0x6a74 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C1 CompilerThread10" #17 daemon prio=9 os_prio=2 tid=0x000000002bccc800 nid=0x727c waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C1 CompilerThread9" #16 daemon prio=9 os_prio=2 tid=0x000000002bcc9000 nid=0x3c34 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C1 CompilerThread8" #15 daemon prio=9 os_prio=2 tid=0x000000002bccc000 nid=0x7814 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread7" #14 daemon prio=9 os_prio=2 tid=0x000000002bccb000 nid=0x6fac waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread6" #13 daemon prio=9 os_prio=2 tid=0x000000002bcca800 nid=0x71a0 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread5" #12 daemon prio=9 os_prio=2 tid=0x000000002bcbe000 nid=0x3fb0 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread4" #11 daemon prio=9 os_prio=2 tid=0x000000002bcb3800 nid=0x8174 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread3" #10 daemon prio=9 os_prio=2 tid=0x000000002bcb2800 nid=0x4760 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread2" #9 daemon prio=9 os_prio=2 tid=0x000000002bcb2000 nid=0x85a4 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread1" #8 daemon prio=9 os_prio=2 tid=0x000000002bcb1000 nid=0x6310 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"C2 CompilerThread0" #7 daemon prio=9 os_prio=2 tid=0x000000002bcaa800 nid=0xbe8 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"Attach Listener" #5 daemon prio=5 os_prio=2 tid=0x00000000292c2000 nid=0x5cd4 waiting on condition [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"Signal Dispatcher" #4 daemon prio=9 os_prio=2 tid=0x000000002926f000 nid=0x7a4c runnable [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE


"VM Thread" os_prio=2 tid=0x00000000272a5800 nid=0x6590 runnable 


"GC task thread#0 (ParallelGC)" os_prio=0 tid=0x00000000039cc800 nid=0x86b0 runnable 


"GC task thread#1 (ParallelGC)" os_prio=0 tid=0x00000000039ce800 nid=0x39cc runnable 


"GC task thread#2 (ParallelGC)" os_prio=0 tid=0x00000000039d0000 nid=0x62b4 runnable 


"GC task thread#3 (ParallelGC)" os_prio=0 tid=0x00000000039d1800 nid=0x1ea8 runnable 


"GC task thread#4 (ParallelGC)" os_prio=0 tid=0x00000000039d3800 nid=0x3324 runnable 


"GC task thread#5 (ParallelGC)" os_prio=0 tid=0x00000000039d6000 nid=0x7f28 runnable 


"GC task thread#6 (ParallelGC)" os_prio=0 tid=0x00000000039d9000 nid=0x81ec runnable 


"GC task thread#7 (ParallelGC)" os_prio=0 tid=0x00000000039da000 nid=0x45d8 runnable 


"GC task thread#8 (ParallelGC)" os_prio=0 tid=0x00000000039db800 nid=0x1d30 runnable 


"GC task thread#9 (ParallelGC)" os_prio=0 tid=0x00000000039dc800 nid=0x4338 runnable 


"GC task thread#10 (ParallelGC)" os_prio=0 tid=0x00000000039dd800 nid=0x7b00 runnable 


"GC task thread#11 (ParallelGC)" os_prio=0 tid=0x00000000039e1000 nid=0x3b8c runnable 


"GC task thread#12 (ParallelGC)" os_prio=0 tid=0x00000000039e2000 nid=0x52b4 runnable 


"GC task thread#13 (ParallelGC)" os_prio=0 tid=0x00000000039e3000 nid=0x8240 runnable 


"GC task thread#14 (ParallelGC)" os_prio=0 tid=0x00000000039e4800 nid=0x14c8 runnable 


"GC task thread#15 (ParallelGC)" os_prio=0 tid=0x00000000039e5800 nid=0x6038 runnable 


"GC task thread#16 (ParallelGC)" os_prio=0 tid=0x00000000039eb800 nid=0x2a10 runnable 


"GC task thread#17 (ParallelGC)" os_prio=0 tid=0x00000000039f1800 nid=0xd3c runnable 


"VM Periodic Task Thread" os_prio=2 tid=0x000000002be26000 nid=0x8984 waiting on condition 
JNI global references: 232


